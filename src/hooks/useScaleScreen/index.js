export function useScaleScreen(options = {}) {
  const display = ref(null)
  const zoomScale = ref(0)
  const aspectScale = ref(0)

  const screenWidth = computed(() => display.value?.workArea?.width || 0)
  const screenHeight = computed(() => display.value?.workArea?.height || 0)

  const containerRef = computed(() => options.containerRef?.value)
  const containerWidth = ref(0)
  const containerHeight = ref(0)

  getPrimaryDisplay()

  watchEffect(() => {
    if (!containerRef?.value || !display.value) {
      return false
    }

    const originalWidth = containerRef.value.offsetWidth
    const originalHeight = containerRef.value.offsetHeight

    zoomScale.value = Math.min(
      originalWidth / display.value.width,
      originalHeight / display.value.height,
    )

    aspectScale.value = screenWidth / screenHeight

    getContainerAspectScaleRect({
      originalWidth,
      originalHeight,
      dimension: 'height',
    })
  })

  async function getPrimaryDisplay() {
    display.value = await window.electron?.ipcRenderer?.invoke('get-primary-display')
  }

  function scaleCalculator(val, real = false) {
    let value = 0

    if (real) {
      value = val / zoomScale.value
    }
    else {
      value = val * zoomScale.value
    }

    return value
  }

  function scaleConverter(val, real) {
    let value = 0

    if (['number', 'string'].includes(typeof val)) {
      value = scaleCalculator(val, real)
    }
    else if (Object.prototype.toString.call(val) === '[object Object]') {
      value = Object.entries(val).reduce((obj, [key, value]) => {
        obj[key] = scaleCalculator(value, real)
        return obj
      }, {})
    }

    return value
  }

  /**
   * 用于获取容器相对于屏幕比例的最大缩放矩形
   * 需要根据屏幕的宽高比，计算出容器最大缩放矩形(容器的宽度和高度)
   * @param {Object} options
   * @param {number} options.originalWidth - 容器原始宽度
   * @param {number} options.originalHeight - 容器原始高度
   * @param {string} options.dimension - 按照哪个维度计算，可选值为 'width'、'height'、'auto'
   */
  function getContainerAspectScaleRect(options = {}) {
    const { originalWidth, originalHeight } = options

    const width = originalWidth
    const height = originalHeight

    containerWidth.value = width
    containerHeight.value = height
  }

  return {
    display,
    zoomScale,
    aspectScale,
    getPrimaryDisplay,
    scaleConverter,
    screenWidth,
    screenHeight,
    containerWidth,
    containerHeight,
  }
}

export default useScaleScreen
